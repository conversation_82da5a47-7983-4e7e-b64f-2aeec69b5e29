import discord
from discord.ext import commands

TOKEN = 'MTM4NDMzNjM3MDEwMDg2NzEzMg.GKFjQS.kO4Nqq7-daQ4-xsgjr02e326PtBM6CpoyIVdoE'

intents = discord.Intents.default()

bot = commands.Bot(command_prefix='!', intents=intents)

@bot.event
async def on_ready():
    print(f'Logged in as {bot.user.name}')

@bot.command()
async def hello(ctx):
    await ctx.send('Hello!')

@bot.command()
async def test(ctx):
    await ctx.send('working!')

bot.run(TOKEN)