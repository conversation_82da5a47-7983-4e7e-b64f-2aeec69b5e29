import discord
from discord.ext import commands

TOKEN = 'YOUR_BOT_TOKEN'

intents = discord.Intents.default()
intents.message_content = True

bot = commands.Bot(command_prefix='!', intents=intents)

@bot.event
async def on_ready():
    print(f'Logged in as {bot.user.name}')

@bot.command()
async def hello(ctx):
    await ctx.send('Hello!')


@bot.command()
async def echo(ctx, *, message):
    await ctx.send(message)

bot.run(TOKEN)